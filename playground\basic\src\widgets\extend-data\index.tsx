import { Button } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const AddExtendData: React.FC = () => {
  const runner = useCommandRunner();

  return (
    <Button
      onClick={() =>
        runner(BuildInCommand.UpdateExtendSenderData, {
          setValue: (prevValue: any) => {
            const key = Math.random().toString();
            return prevValue ? { ...prevValue, [key]: 1 } : { [key]: 1 };
          },
        })
      }
      size="small"
      shape="round"
    >
      添加ExtendData
    </Button>
  );
};

export default AddExtendData;
