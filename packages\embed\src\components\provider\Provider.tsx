import React, { PropsWithChildren } from "react";

import { AgentChatConfig, HostWrapper } from "@cscs-agent/core";

interface AgentEmbedProviderProps {
  agentChatConfig: AgentChatConfig;
  agentCode: string;
}

const AgentEmbedProvider: React.FC<PropsWithChildren<AgentEmbedProviderProps>> = (props) => {
  const { agentChatConfig, agentCode, children } = props;

  return (
    <HostWrapper agentChatConfig={agentChatConfig} embedded={true} agentCode={agentCode}>
      {children}
    </HostWrapper>
  );
};

export default AgentEmbedProvider;
