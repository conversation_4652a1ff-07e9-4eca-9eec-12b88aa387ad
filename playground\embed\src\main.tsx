import "@@/antd/dist/antd.css";
import "@@/@cscs-agent/agents/dist/agents-tailwind.css";
import "@@/@cscs-agent/core/dist/core-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";

import "./styles.css";

import dayjs from "dayjs";
import React from "react";
import ReactDOM from "react-dom";
import { RouterProvider, createBrowserRouter } from "react-router-dom";

import { setAgentGlobalConfig } from "@cscs-agent/core";

import App from "./app";
import Agent from "./pages/agent";
import AgentApp from "./pages/agent-app";
import Chat from "./pages/chat";
import Login from "./pages/login/Login";

dayjs.locale("zh-cn");

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "agent",
        element: <AgentApp />,
      },
      {
        path: "chat/:id",
        element: <Chat />,
      },
      {
        path: "agent/:code",
        element: <Agent />,
      },
    ],
  },
  {
    path: "/login",
    element: <Login />,
  },
]);

setAgentGlobalConfig({
  defaultRequestInterceptorOptions: {
    prefix: "/agent-api",
  },
  defaultErrorHandlerOption: {
    redirectToLogin: false,
    errorHandles: {
      401: () => {
        location.href = "/login";
      },
    },
  },
  tokenHandles: {
    getToken() {
      const tokenStr = localStorage.getItem("agent_token");

      if (tokenStr) {
        try {
          // 尝试解析JSON字符串
          return JSON.parse(tokenStr).access_token;
        } catch (e) {
          console.error("Failed to parse token:", e);
        }
      }
      return null;
    },
    setToken(token: any) {
      localStorage.setItem("agent_token", JSON.stringify({ access_token: token }));
    },
    clearToken() {
      localStorage.removeItem("agent_token");
    },
  },
}).then(() => {
  ReactDOM.render(<RouterProvider router={router} />, document.getElementById("root"));
});
