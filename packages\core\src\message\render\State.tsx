import { getProperty, setProperty } from "dot-prop";
import React, { useContext, useEffect } from "react";

import { StateSet, StateUpdateStrategy } from "@/types";

import MessageContext from "../ui/Context";

interface StateProps {
  setList: StateSet[];
}

const State: React.FC<StateProps> = (props) => {
  const { setList } = props;
  const messageContext = useContext(MessageContext);
  const setMessageState = messageContext?.setMessageState;

  useEffect(() => {
    if (!setMessageState) return;

    setMessageState((prevValues) => {
      for (const set of setList) {
        const prevValue = getProperty(prevValues, set.path);
        if (typeof prevValue === "string" && set.strategy === StateUpdateStrategy.IncrementalMerge) {
          let currentValue = "";
          if (typeof currentValue === "number") {
            currentValue = String(currentValue);
          }
          if (typeof currentValue === "string") {
            currentValue = set.value as string;
          }
          const newValue = prevValue + currentValue;
          setProperty(prevValues, set.path, newValue);
        } else {
          setProperty(prevValues, set.path, set.value);
        }
      }
      return structuredClone(prevValues);
    });
  }, [setList]);

  return null;
};

export default State;
