import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderFooter: React.FC = () => {
  const agentConfig = useActiveAgentConfig();
  const direction = agentConfig?.sender?.slots?.footer?.direction ?? "ltr";

  const widgets = useMemo(() => {
    return agentConfig?.sender?.slots?.footer?.widgets ?? [];
  }, [agentConfig]);

  return (
    <div className={`ag:pt-2 ag:flex ag:flex-wrap ${direction === "rtl" ? "ag:flex-row-reverse" : ""}`}>
      {widgets.map((Widget, index) => (
        <div className="ag:mr-2" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderFooter;
