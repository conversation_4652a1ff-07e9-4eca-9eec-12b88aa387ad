import React, { useEffect, useState } from "react";

import { useCommandRunner } from "@/command";
import { Message, createMessage } from "@/core/common/message";
import { useActiveAgentCode, useActiveConversationId, useIsLoadingMessage, useMessages } from "@/core/state/store";
import { get } from "@/request";
import { BuildInCommand, ConversationData, MessageStatus, Role, StandardResponse } from "@/types";
import { UserOutlined } from "@ant-design/icons";

import Bubble from "./Bubble";

const MessageContainer = () => {
  const [messages, setMessages] = useMessages();
  const [activeConversationId] = useActiveConversationId();
  const [loading, setLoading] = useState(false);
  const [isLoadingMessage] = useIsLoadingMessage();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const runner = useCommandRunner();
  const search = new URLSearchParams(window.location.search);

  useEffect(() => {
    if (activeConversationId) {
      if (!isLoadingMessage) {
        setLoading(true);
        setMessages([]);
      }
      get("/message", {
        conversation_id: activeConversationId,
      })
        .then((res: any) => {
          // TODO any类型
          const list = res.data.data;
          if (Array.isArray(list)) {
            const historyMessages = list
              .map((i) =>
                createMessage(i.id, i.message_type, i.content, MessageStatus.Finished, i.agent_code, i.user_rating),
              )
              .filter(Boolean) as Message[];
            // TODO: 优化加载逻辑
            setMessages((messages) => {
              const lastMessage = messages[messages.length - 1];
              if (messages.length === 0 || lastMessage.status !== MessageStatus.Loading) {
                return historyMessages;
              } else {
                return [...messages];
              }
            });
            loadUnfinishedMessage();
          }
        })
        .catch((error) => {
          if (error.status !== 404) {
            setMessages([]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [activeConversationId]);

  const loadUnfinishedMessage = () => {
    get<StandardResponse<ConversationData>>(`/conversation/${activeConversationId}`).then((res) => {
      const data = res.data.data;
      const isNew = search.get("isNew");
      setActiveAgentCode(data.current_agent_code);
      if (data.task_id && !isLoadingMessage && !isNew) {
        setTimeout(() => {
          runner(BuildInCommand.LoadUnfinishedMessage, {
            message: "",
            agentCode: data.current_agent_code,
            conversationId: activeConversationId,
            isNewConversation: false,
          });
        }, 300);
      }
    });
  };

  return (
    <>
      {loading && (
        <div className="ag:flex ag:items-center">
          <div className="ag:animate-spin ag:circle-loader"></div>
        </div>
      )}
      <div className="ag:flex ag:flex-col ag:items-stretch ag:gap-4">
        {messages.map((item) => (
          <Bubble
            key={item.id}
            message={item}
            avatar={{ icon: <UserOutlined />, style: { background: item.role === Role.HUMAN ? "#87d068" : "#3399ff" } }}
            placement={item.role === Role.HUMAN ? "end" : "start"}
            role={item.role}
          />
        ))}
      </div>
    </>
  );
};

export default MessageContainer;
