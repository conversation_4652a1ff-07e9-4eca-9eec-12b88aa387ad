import React, { useImperative<PERSON>and<PERSON>, useMemo } from "react";

import { AgentChatConfig, RouterConfig, RouterProvider, createDefaultRouter } from "@cscs-agent/core";

interface AppProps {
  routerConfig: RouterConfig;
  agentChatConfig: AgentChatConfig;
}

interface AppRef {
  router: ReturnType<typeof createDefaultRouter>;
}

const AgentAppEmbedded = React.forwardRef<AppRef, AppProps>(function AppEmbedded(props, ref) {
  const { agentChatConfig, routerConfig } = props;

  const router = useMemo(() => {
    routerConfig.memory = true;
    return createDefaultRouter(routerConfig, agentChatConfig);
  }, [routerConfig, agentChatConfig]);

  useImperativeHandle(
    ref,
    () => ({
      router,
    }),
    [router],
  );

  return <RouterProvider router={router} />;

  // return (
  //   router && (
  //     <UNSAFE_RouteContext.Provider
  //       value={{
  //         outlet: null,
  //         matches: [],
  //         isDataRoute: false,
  //       }}
  //     >
  //       <UNSAFE_NavigationContext.Provider value={null as any}>
  //         <UNSAFE_LocationContext.Provider value={null as any}>
  //           <RouterProvider router={router} />
  //         </UNSAFE_LocationContext.Provider>
  //       </UNSAFE_NavigationContext.Provider>
  //     </UNSAFE_RouteContext.Provider>
  //   )
  // );
});

export default AgentAppEmbedded;
