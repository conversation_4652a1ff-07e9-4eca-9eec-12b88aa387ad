/* eslint-disable react-hooks/rules-of-hooks */
import { NavigateOptions, useNavigate as useRouterNavigate } from "react-router-dom";

import { NavigateGuard } from "@/types";

import { useActiveAgentConfig } from "../core/hooks/useAgent";
import { useIsLoadingMessage } from "../core/state/store";

// const chatInterruptAlertGuard: NavigateGuard = {
//   id: "chatInterruptAlertGuard",
//   guard: (_, info) => {
//     return new Promise((resolve, reject) => {
//       if (info.isLoadingMessage) {
//         Modal.confirm({
//           content: "消息正在接收中，是否中断当前会话？",
//           onOk: () => resolve(true),
//           onCancel: () => reject(new Error("消息正在接收中，用户取消跳转")),
//         });
//       } else {
//         resolve(true);
//       }
//     });
//   },
// };

const navigateGuards: NavigateGuard[] = [];

export function addNavigateGuard(guard: NavigateGuard) {
  navigateGuards.push(guard);
}

export function deleteNavigateGuard(id: string) {
  navigateGuards.splice(
    navigateGuards.findIndex((i) => i.id === id),
    1,
  );
}

export const useNavigate = () => {
  const routerNavigate = useRouterNavigate();
  const [isLoadingMessage] = useIsLoadingMessage();
  const activeAgentConfig = useActiveAgentConfig();

  const navigate = (to: string, options?: NavigateOptions) => {
    const info = {
      agent: activeAgentConfig!,
      isLoadingMessage,
    };

    return Promise.all(navigateGuards.map((i) => i.guard(to, info))).then(() => {
      routerNavigate(to, options);
      return true;
    });
  };

  return navigate;
};
