import React from "react";
import { useParams } from "react-router-dom";

import { AgentEmbedProvider } from "@cscs-agent/embed";
import { ChatEmbedded } from "@cscs-agent/presets";

import { config } from "../../agent-config";

const ChatPage: React.FC = () => {
  const { id } = useParams();

  return (
    <div className="h-[600px]">
      <AgentEmbedProvider agentChatConfig={config} agentCode="default">
        <ChatEmbedded id={id} />
      </AgentEmbedProvider>
    </div>
  );
};

export default ChatPage;
