import { Select, Tooltip } from "antd";
import React, { useEffect } from "react";

import { BuildInCommand, get, useCommandRunner } from "@cscs-agent/core";

interface DataSourceSelectProps {
  dataSourceApiUrl: string;
}

interface DataSourceResponse {
  name: string;
  db_type: string;
  host: string;
  port: number;
  username: string;
  database_name: string;
  id: number;
  created_at: string;
  updated_at: string;
}

const DataSourceSelect: React.FC<DataSourceSelectProps> = (props) => {
  const { dataSourceApiUrl } = props;
  const [options, setOptions] = React.useState<{ label: string; value: number }[]>([]);
  const runner = useCommandRunner();
  const [value, setValue] = React.useState<number>();

  useEffect(() => {
    if (dataSourceApiUrl) {
      get<DataSourceResponse[]>(dataSourceApiUrl).then((res) => {
        const $options = res.data.map((i) => ({
          label: i.name,
          value: i.id,
        }));
        setOptions($options);
        setValue($options[0]?.value);
      });
    }
  }, [dataSourceApiUrl]);

  useEffect(() => {
    if (value !== undefined) {
      runner(BuildInCommand.UpdateExtendSenderParams, {
        setValue: (prevValue: any) => {
          prevValue.dataSourceId = value;
          return prevValue;
        },
      });
    }
  }, [value]);

  return (
    <Tooltip title="切换数据源">
      <Select
        className="ats:w-35"
        options={options}
        onChange={setValue}
        value={value}
        bordered={false}
        placeholder="请选择数据源"
      />
    </Tooltip>
  );
};

export default DataSourceSelect;
