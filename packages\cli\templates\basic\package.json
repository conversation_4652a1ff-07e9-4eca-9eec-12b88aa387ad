{"name": "basic", "version": "0.5.0", "private": true, "description": "", "type": "module", "scripts": {"dev": "agent-builder start", "build": "agent-builder build", "lint": "eslint ./src --ext .ts,.tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@cscs-agent/builder": "^0.5.0", "@cscs-agent/core": "^0.5.0", "@cscs-agent/icons": "^0.5.0", "@cscs-agent/presets": "^0.5.0", "@cscs-agent/mock": "^0.5.0", "antd": "^5.24.3", "dayjs": "^1.11.13", "react": "16.12.0", "react-dom": "16.12.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/react": "^16.9.34", "@types/react-dom": "^16.9.9", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "typescript": "^5.8.2", "vite": "^6.2.0", "vite-plugin-mock-dev-server": "^1.9.1"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=22.15.0", "pnpm": ">=10.9.0"}, "packageManager": "pnpm@10.9.0"}