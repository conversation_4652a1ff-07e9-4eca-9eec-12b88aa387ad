import React from "react";
import ReactDOM from "react-dom";
import { RouterProvider } from "react-router-dom";

import {
  DefaultErrorHandlerOption,
  DefaultRequestInterceptorOptions,
  setDefaultErrorHandlerOptions,
  setDefaultRequestInterceptorOptions,
} from "@/request";
import { RouterConfig, createDefaultRouter } from "@/router";
// import { antdThemeTokens } from "@/theme/antd-theme";
import { AgentChatConfig } from "@/types";
import { setBaseUrl } from "@/utils";

import { TokenHandlesSettingOption, setTokenHandles, tokenHandles } from "../common/token";
import { setLoginUrl } from "../common/vars";

export type InitAppOptions = {
  routerConfig?: RouterConfig;
  customCreateAppRoot?: () => void;
  agentChatConfig?: AgentChatConfig;
  lifecycle?: {
    onBeforeInit?: () => Promise<void> | void;
    onAfterInit?: () => Promise<void> | void;
    onError?: (error: Error) => Promise<void> | void;
  };
  embedded?: boolean;
} & AgentGlobalConfigOptions;

export interface AgentGlobalConfigOptions {
  defaultRequestInterceptorOptions?: Partial<DefaultRequestInterceptorOptions>;
  defaultErrorHandlerOption?: Partial<DefaultErrorHandlerOption>;
  baseUrl?: string;
  loginUrl?: string;
  tokenHandles?: TokenHandlesSettingOption;
  logoShow?: boolean;
}

export async function initApp(options: InitAppOptions) {
  const {
    routerConfig,
    defaultRequestInterceptorOptions,
    defaultErrorHandlerOption,
    baseUrl,
    loginUrl,
    customCreateAppRoot,
    agentChatConfig,
    logoShow = true,
    lifecycle,
    embedded = false,
    tokenHandles,
  } = options;
  const { onBeforeInit, onAfterInit, onError } = lifecycle || {};

  try {
    if (onBeforeInit) {
      await onBeforeInit();
    }

    /**
     * 非嵌入模式，直接创建路由组件, 设置全局配置
     */
    if (!embedded) {
      loadTokenFromUrl();

      setAgentGlobalConfig({
        defaultRequestInterceptorOptions,
        defaultErrorHandlerOption,
        baseUrl,
        loginUrl,
        tokenHandles,
        logoShow,
      });

      if (!agentChatConfig) {
        console.error("Missing agentChatConfig!");
        return;
      }
      // 初始化路由
      if (!customCreateAppRoot) {
        if (routerConfig) {
          createAppRoot({
            routerConfig,
            agentChatConfig,
          });
        } else {
          console.error("Router is not defined");
        }
      } else {
        customCreateAppRoot();
      }
    }

    if (onAfterInit) {
      await onAfterInit();
    }
  } catch (error) {
    if (onError) {
      await onError(error as Error);
    } else {
      console.error("App initialization error:", error);
    }
  }
}

export async function setAgentGlobalConfig(options: AgentGlobalConfigOptions) {
  const { defaultRequestInterceptorOptions, defaultErrorHandlerOption, baseUrl, loginUrl, tokenHandles, logoShow } =
    options;

  if (logoShow) {
    printLogoShow();
  }

  // 设置默认请求拦截器选项
  if (defaultRequestInterceptorOptions) {
    setDefaultRequestInterceptorOptions(defaultRequestInterceptorOptions);
  }
  // 设置默认错误处理选项
  if (defaultErrorHandlerOption) {
    setDefaultErrorHandlerOptions(defaultErrorHandlerOption);
  }
  // 设置基础 URL 和登录 URL
  if (baseUrl) {
    setBaseUrl(baseUrl);
  }
  // 设置登录 URL
  if (loginUrl) {
    setLoginUrl(loginUrl);
  }

  if (tokenHandles) {
    setTokenHandles(tokenHandles);
  }
}

function createAppRoot(config: {
  routerConfig: RouterConfig;
  agentChatConfig: AgentChatConfig;
  embedded?: boolean;
  container?: ReactDOM.Container;
}) {
  const { routerConfig, agentChatConfig, container } = config;

  const router = createDefaultRouter(routerConfig, agentChatConfig);

  ReactDOM.render(
    // <ConfigProvider
    //   locale={zhCN}
    //   theme={{
    //     cssVar: true,
    //     hashed: false,
    //     token: {
    //       ...antdThemeTokens,
    //     },
    //   }}
    // >
    //   <RouterProvider router={router}></RouterProvider>
    // </ConfigProvider>
    <RouterProvider router={router}></RouterProvider>,
    container ?? document.getElementById("root"),
  );
}

function loadTokenFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  if (token) {
    tokenHandles.setToken(token);
  }
}

function printLogoShow() {
  const logo = `
  ████     ████   █████ ██    ██ █████        █ █
██    ██ ██         ██       ███  ██    ██    █    █ █
██████ ██  ███ █████ ██ █ ██    ██         █ █
██    ██ ██    ██ ██       ██  ███    ██    █  █ █
██    ██   ████   █████ ██    ██    ██       █ █
  `;
  console.log(logo);
}
